<!-- ShopTunes Embedded Script v2.0.0 -->
<!-- Embeds Spotify music players into external websites -->

<script src="https://open.spotify.com/embed/iframe-api/v1" async></script>
<script type="module" src="./logger.ts"></script>

<!--
Usage Examples:

1. Demo Mode (for testing):
   Add ?shoptunes-demo=0 to your URL

2. Custom Configuration:
   <script
     data-shoptunes-config='{"isEnable":true,"playlistUrl":"https://open.spotify.com/playlist/37i9dQZF1DXcBWIGoYBM5M","position":"0","height":"0","customHeight":"","theme":"light"}'
     src="./logger.ts">
   </script>

3. Debug Mode:
   Add ?shoptunes-debug to your URL or set localStorage.setItem('shoptunes-debug', 'true')

For complete documentation, see README.md
-->
