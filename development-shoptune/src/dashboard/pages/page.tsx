import { zodResolver } from "@hookform/resolvers/zod";
import { dashboard } from "@wix/dashboard";
import React, { type FC } from "react";
import { FormProvider, useForm } from "react-hook-form";

import "@/dashboard/styles/global.css";
import { <PERSON><PERSON>, <PERSON>, WixDesignSystemProvider } from "@wix/design-system";
import "@wix/design-system/styles.global.css";
import BannerEnable from "../components/banner-enable";
import Customize from "../components/customize";
import EmbedPlaylist from "../components/embed-playlist";
import Preview from "../components/preview";
import { isEqual } from "../lib/is-equal";
import { shopTunesDefault, ShopTunesType } from "./constant";
import { shopTunesSchema } from "./schema";

const Index: FC = () => {
	const form = useForm<ShopTunesType>({
		defaultValues: shopTunesDefault,
		resolver: zodResolver(shopTunesSchema),
		mode: "onChange",
	});
	const difference = isEqual(form.watch(), shopTunesDefault);

	return (
		<WixDesignSystemProvider features={{ newColorsBranding: true }}>
			<Page>
				<Page.Header
					title="ShopTunes"
					subtitle="Instantly bring Spotify music to your website!"
					actionsBar={
						!difference ? (
							<div className="flex gap-3">
								{/* 'standard' | 'inverted' | 'destructive' | 'premium' | 'dark' | 'light' | 'transparent' | 'premium-light' | 'ai' */}
								<Button
									skin="inverted"
									onClick={() => {
										dashboard.showToast({
											message: "Your first toast message!",
										});
									}}>
									Cancel
								</Button>
								<Button
									onClick={() => {
										dashboard.showToast({
											message: "Your first toast message!",
										});
									}}>
									Save
								</Button>
							</div>
						) : null
					}
				/>
				<Page.Content>
					<FormProvider {...form}>
						<div className="space-y-4">
							<BannerEnable />
							<div className="grid md:grid-cols-2 gap-4">
								<div className="space-y-4">
									<EmbedPlaylist />
									<Customize />
								</div>
								<Preview />
							</div>
						</div>
					</FormProvider>
				</Page.Content>
			</Page>
		</WixDesignSystemProvider>
	);
};

export default Index;
