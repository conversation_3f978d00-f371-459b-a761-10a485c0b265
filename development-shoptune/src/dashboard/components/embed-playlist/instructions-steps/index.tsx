import React, { <PERSON> } from "react";

import { Box, Heading, Text } from "@wix/design-system";

const InstructionsSteps: FC = () => {
	const handleNavigateToSpotify = () => {
		window.open("https://open.spotify.com", "_blank");
	};
	return (
		<Box display="block" className="mt-5">
			<Heading size="medium">How to add your playlist on your site?</Heading>
			<div className="flex flex-col mt-3">
				<Text weight="normal">
					1. Go to{" "}
					<Text
						skin="success"
						className="cursor-pointer"
						onClick={handleNavigateToSpotify}>
						Spotify
					</Text>
				</Text>
				<Text weight="normal">2. Select your favorite playlist</Text>
				<Text weight="normal">3. Click “...” on screen</Text>
				<Text weight="normal">4. Choose “Share” and “Copy link”</Text>
				<Text weight="normal">5. Then paste it in the “URL Spotify”</Text>
				<Text weight="normal">6. Finally, click “Embed”</Text>
			</div>
		</Box>
	);
};

export default InstructionsSteps;
