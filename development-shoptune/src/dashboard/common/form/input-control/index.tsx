import React from "react";
import {
	Controller,
	FieldPath,
	FieldValues,
	useFormContext,
} from "react-hook-form";

import { Box, FormField, Input, InputProps, Text } from "@wix/design-system";

export type InputControlProps<TForm extends FieldValues> = Omit<
	InputProps,
	"value" | "onChange"
> & {
	name: FieldPath<TForm>;
	label: string;
};

const InputControl = <TForm extends FieldValues>({
	label,
	name,
	...props
}: InputControlProps<TForm>) => {
	const { control } = useFormContext<TForm>();

	return (
		<Controller
			control={control}
			name={name}
			render={({ field, fieldState }) => (
				<Box display="block" flex="1">
					<Text secondary>{label}</Text>
					<FormField
						{...(fieldState.error && {
							status: "error",
						})}
						statusMessage={fieldState.error?.message}
						classNames="mt-2">
						<Input {...field} {...props} />
					</FormField>
				</Box>
			)}
		/>
	);
};

export default InputControl;
